import { WidgetCustomization } from './customization-types';

export const defaultCustomization: WidgetCustomization = {
  layout: {
    padding: '1.5rem', // Reduced from 2.5rem to 1.5rem
    paddingTop: '1.25rem',
    margin: '2rem',
    borderRadius: '16px',
    maxWidth: '400px',
    minWidth: '300px', // NEW: Minimum width
    width: 'auto', // NEW: Fixed width (auto = responsive)
    height: 'auto', // NEW: Fixed height (auto = content-based)
    minHeight: 'auto', // NEW: Minimum height
    maxHeight: 'auto' // NEW: Maximum height
  },
  colors: {
    background: '#ffffff',
    buttonBackground: '#000000',
    buttonText: '#ffffff',
    inputBackground: '#ffffff',
    inputText: '#000000',
    borderColor: '#e5e5e5'
  },
  typography: {
    titleText: {
      signinText: 'Sign In',
      signupText: 'Create Account',
      resetText: 'Reset Password',
      updateText: 'Update Password',
      verifyText: 'Verify Email',
      resendText: 'Resend Confirmation'
    },
    subtitleText: {
      signinText: '',
      signupText: '',
      resetText: '',
      updateText: '',
      verifyText: '',
      resendText: ''
    },
    titleSize: '2rem',
    titleColor: '#1a1a1a',
    labelSize: '0.9rem',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    labelColor: '#333333',
    labelFontWeight: '400',
    titleAlignment: 'center',
    titleWeight: '600',
    titleLineHeight: '1.2',
    termsText: {
      agreePrefix: 'I agree with the',
      andConnector: 'and',
      defaultPrefix: 'default',
      linkText: {
        terms: 'Terms of Service',
        privacy: 'Privacy Policy',
        notifications: 'Notification Settings'
      },
      textColor: '#333333',
      linkColor: '#000000'
    },
    navTextColor: '#1a1a1a',
    navTextColorDark: '#ffffff'
  },
  inputs: {
    emailPlaceholder: 'Email Address',
    passwordPlaceholder: 'Password',
    usernamePlaceholder: 'Username',
    confirmPasswordPlaceholder: 'Confirm Password',
    emailLabel: 'Email',
    passwordLabel: 'Password',
    usernameLabel: 'Username',
    confirmPasswordLabel: 'Confirm Password',
    borderRadius: '4px',
    height: '50px',
    width: '100%',
    padding: '0 1rem',
    margin: '0 0 1rem 0',
    fontSize: '1rem',
    fontWeight: '400',
    focusBorderColor: '#000000',
    focusBoxShadow: 'none',
    placeholderAlign: 'left'
  },
  buttons: {
    signinText: 'Sign In',
    signupText: 'Create Account',
    resetText: 'Reset Password',
    updateText: 'Update Password',
    verifyText: 'Verify Email',
    resendText: 'Resend Confirmation',
    height: '40px',
    width: '100%',
    borderRadius: '4px',
    hoverBackground: '#27272a'
  },
  navLinks: {
    signinPrompt: 'Already have an account?',
    signinLinkText: 'Sign In',
    signupPrompt: "Don't have an account?",
    signupLinkText: 'Sign Up',
    forgotPrompt: 'Forgot Password?',
    forgotLinkText: 'Reset',
    fontSize: '0.95rem',
    color: '#1a1a1a',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    textAlign: 'center',
    marginTop: '1.5rem',
    marginBottom: '0',
    fontWeight: '400',
    linkColor: '#0070f3',
    linkFontWeight: '500',
    backToSigninPrompt: 'Back to Sign In?'
  }
};




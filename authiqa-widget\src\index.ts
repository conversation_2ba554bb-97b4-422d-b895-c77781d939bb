import {
    AuthUrls,
    WidgetConfig,
    ApiResponse,
    AuthResponse,
    SignupSuccessResponse,
    SignupErrorResponse,
    SignupResponse,
    SigninSuccessResponse,
    SigninErrorResponse,
    SigninResponse,
    ResendConfirmationResponse
} from './lib/types';
import { WidgetCustomization } from './lib/customization-types';
import { Modal } from './components/Modal';
import { AuthFrame } from './components/AuthFrame';
import { ApiService } from './lib/api';
import { EventManager } from './lib/eventManager';
import { getStyleContent, getComponentStyles, generateTermsContainerStyles } from './lib/styles';
import { StyleGenerator } from './lib/style-generator';
import './styles/default-theme.css';

declare global {
    interface Window {
        AuthiqaGlobalConfig?: {
            customization?: Partial<WidgetCustomization>;
            messages?: Record<string, string>;
        };
        _authiqaGoogleOneTapDismissed?: boolean;
        _authiqaGoogleOneTapPromptActive?: boolean;
        _authiqaGoogleOneTapSuccessfulAuth?: boolean;
    }
}


export class AuthiqaWidget {
    private config: WidgetConfig;
    private authUrls: AuthUrls | null = null;
    private api: ApiService; // Reintroducing the api property
    private currentAction: keyof AuthUrls | null = null; // Track the current action for custom messages
    private googleSsoConfig?: { enabled: boolean; clientId: string }; // Store Google SSO config
    private githubSsoConfig?: { enabled: boolean; clientId: string }; // Store GitHub SSO config
    private emailVerificationRequired: boolean = false; // Store email verification requirement (default to false to match backend default)

    constructor(config: WidgetConfig) {
        // Merge global config with provided config
        if (window.AuthiqaGlobalConfig) {
            // Merge customization
            if (window.AuthiqaGlobalConfig.customization) {
                config.customization = {
                    ...window.AuthiqaGlobalConfig.customization,
                    ...config.customization
                };
            }

            // Merge messages
            if (window.AuthiqaGlobalConfig.messages) {
                config.messages = {
                    ...window.AuthiqaGlobalConfig.messages,
                    ...config.messages
                };
            }
        }

        this.config = config;
        this.api = new ApiService(config); // Initialize the ApiService
        this.injectStyles(); // Add this line
    }

    /**
     * Store access token in localStorage and refresh token in sessionStorage
     */
    private storeTokens(tokenData: any): void {
        // Handle the actual API response structure
        if (typeof tokenData === 'string') {
            // Single JWT token (current API response)
            localStorage.setItem('access_token', tokenData);
            // For now, we'll store the same token as refresh token since API only returns one
            sessionStorage.setItem('refresh_token', tokenData);
        } else if (tokenData.accessToken && tokenData.refreshToken) {
            // Future API response with separate tokens
            localStorage.setItem('access_token', tokenData.accessToken);
            sessionStorage.setItem('refresh_token', tokenData.refreshToken);
        }
    }

    /**
     * Check if user has stored authentication tokens
     */
    private isUserAuthenticated(): boolean {
        const accessToken = localStorage.getItem('access_token');
        const refreshToken = sessionStorage.getItem('refresh_token');
        return !!(accessToken || refreshToken);
    }

    /**
     * Get stored authentication token for API requests
     */
    private getStoredToken(): string | null {
        return sessionStorage.getItem('refresh_token') || localStorage.getItem('access_token');
    }

    getAuthUrls(): AuthUrls {
        if (!this.authUrls) {
            throw new Error('Widget not initialized. Call initialize() first.');
        }
        return this.authUrls;
    }

    async initialize(): Promise<void> {
        try {
            const response = await this.api.getOrganizationDetails();
            this.authUrls = response.authUrls;
            // Store googleSsoConfig if present
            if (response.googleSsoConfig) {
                this.googleSsoConfig = response.googleSsoConfig;
            }
            // Store githubSsoConfig if present
            if (response.githubSsoConfig) {
                this.githubSsoConfig = response.githubSsoConfig;
            }
            // Store email verification requirement (default to false to match backend default)
            this.emailVerificationRequired = response.emailVerificationRequired ?? false;
            // Store JWT secret if present
            if ((response as any).jwtSecret) {
                localStorage.setItem('jwt_secret', (response as any).jwtSecret);
            }

            // Use optional chaining with default to true for backward compatibility
            if (response.domainRestrictionEnabled ?? true) {
                const isValidDomain = this.validateDomain(response.organizationUrl);
                if (!isValidDomain) {
                    this.showUnauthorizedError();
                    return;
                }
            }
            // Rest of the initialization code...

            if (this.currentAction === 'signin') {
                this.renderSignInForm();
            }
        } catch (error) {
            console.warn('Failed to fetch organization details:', error);
            // Set default authUrls instead of throwing
            const apiBase = this.api.getApiBase();
            this.authUrls = {
                signin: `${apiBase}/auth/signin`,
                signup: `${apiBase}/auth/signup`,
                verify: `${apiBase}/auth/verify`,
                reset: `${apiBase}/auth/reset`,
                update: `${apiBase}/auth/update`,
                resend: `${apiBase}/auth/resend`,
                successful: `${apiBase}/auth/successful`
            };
        }
    }

    show(action: keyof AuthUrls): void {
        // Dismiss Google One Tap when switching actions in SPAs to prevent interference
        if (this.currentAction && this.currentAction !== action) {
            this.dismissGoogleOneTap();
        }

        this.currentAction = action;

        // Show the form immediately
        if (action === 'verify') {
            this.handleEmailVerification();
        } else if (action === 'signin') {
            this.renderSignInForm();
        } else if (action === 'signup') {
            this.renderSignUpForm();
        } else if (action === 'reset') {
            this.renderResetPasswordForm();
        } else if (action === 'update') {
            this.renderUpdatePasswordForm();
        } else if (action === 'resend') {
            this.renderResendConfirmationForm();
        }

        // Initialize in background if not already done
        if (!this.authUrls) {
            this.initialize().catch(error => {
                console.warn('Failed to fetch organization details:', error);
            });
        }
    }

    private initializeContainer(): HTMLElement {
        let container = document.getElementById(this.config.container);

        if (!container) {
            container = document.createElement('div');
            container.id = this.config.container;
            document.body.appendChild(container);
        }

        // Apply base container class
        container.className = 'authiqa-container';


  // Apply page layout customizations if present
  if (this.config.customization?.pageLayout) {
    const pageLayout = this.config.customization.pageLayout;

  

    // Apply positioning styles to container
    if (pageLayout.formPosition) {
        document.body.style.display = 'flex';
        document.body.style.minHeight = '100vh';

        // Set alignment based on position
        switch (pageLayout.formPosition) {
            case 'top':
                document.body.style.alignItems = 'flex-start';
                document.body.style.justifyContent = 'center';
                break;
            case 'bottom':
                document.body.style.alignItems = 'flex-end';
                document.body.style.justifyContent = 'center';
                break;
            case 'left':
                document.body.style.alignItems = 'center';
                document.body.style.justifyContent = 'flex-start';
                break;
            case 'right':
                document.body.style.alignItems = 'center';
                document.body.style.justifyContent = 'flex-end';
                break;
            default: // center
                document.body.style.alignItems = 'center';
                document.body.style.justifyContent = 'center';
        }
    }

    // Apply margins to container
    if (pageLayout.formMarginTop) container.style.marginTop = pageLayout.formMarginTop;
    if (pageLayout.formMarginBottom) container.style.marginBottom = pageLayout.formMarginBottom;
    if (pageLayout.formMarginLeft) container.style.marginLeft = pageLayout.formMarginLeft;
    if (pageLayout.formMarginRight) container.style.marginRight = pageLayout.formMarginRight;
}
        // Only apply theme if no customization is present
        if (!this.config.customization && !this.config.disableStyles) {
            if (this.config.theme === 'dark') {
                document.body.setAttribute('data-theme', 'dark');
            } else {
                document.body.removeAttribute('data-theme');
            }

            if (this.config.theme !== 'none') {
                container.setAttribute('data-theme', this.config.theme || 'light');
            }
        }

        return container;
    }

   
    private createLabeledInput(
        type: string,
        id: string,
        placeholder: string,
        labelText: string,
        required: boolean = true
    ): { container: HTMLDivElement; input: HTMLInputElement } {
        const container = document.createElement('div');
        container.className = 'labeled-input-container';
        container.classList.add('authiqa-labeled-input');

        // Create label
        const label = document.createElement('label');
        label.setAttribute('for', `authiqa-${id}`);
        label.textContent = labelText;
        label.classList.add('authiqa-label');

        // Create input
        const input = document.createElement('input');
        input.setAttribute('type', type);
        input.setAttribute('id', `authiqa-${id}`);
        input.setAttribute('name', id);
        input.setAttribute('placeholder', placeholder);
        input.setAttribute('required', required ? 'true' : 'false');
        input.classList.add('authiqa-input');

        // Add minimum length for password fields
        if (type === 'password') {
            input.setAttribute('minlength', '6');
        }

        container.appendChild(label);
        container.appendChild(input);

        return { container, input };
    }

    private createPasswordField(placeholder: string, id: string, label?: string): { container: HTMLDivElement; input: HTMLInputElement } {
        const container = document.createElement('div');
        container.classList.add('authiqa-labeled-input');

        // Create label if provided
        if (label) {
            const labelElement = document.createElement('label');
            labelElement.setAttribute('for', `authiqa-${id}`);
            labelElement.textContent = label;
            labelElement.classList.add('authiqa-label');
            container.appendChild(labelElement);
        }

        // Create password container
        const passwordContainer = document.createElement('div');
        passwordContainer.className = 'password-field-container';
        passwordContainer.classList.add('authiqa-password-container');

        // Create input
        const input = document.createElement('input');
        input.setAttribute('type', 'password');
        input.setAttribute('id', `authiqa-${id}`);
        input.setAttribute('name', id);
        input.setAttribute('placeholder', placeholder);
        input.setAttribute('required', 'true');
        input.setAttribute('minlength', '6');
        input.classList.add('authiqa-input');

        // Add input to password container
        passwordContainer.appendChild(input);

        // Create toggle button
        const toggleButton = document.createElement('button');
        toggleButton.setAttribute('type', 'button');
        toggleButton.classList.add('password-toggle');
        toggleButton.innerHTML = '👁️';

        toggleButton.addEventListener('click', () => {
            const type = input.getAttribute('type');
            input.setAttribute('type', type === 'password' ? 'text' : 'password');
            toggleButton.innerHTML = type === 'password' ? '👁️‍🗨️' : '👁️';
        });

        // Add toggle to password container
        passwordContainer.appendChild(toggleButton);

        // Create password validation container
        const validationContainer = document.createElement('div');
        validationContainer.classList.add('password-validation-container');

        // Create validation items
        const validations = [
            { id: 'length', text: '8+ Characters long', check: (val: string) => val.length >= 8 },
            { id: 'uppercase', text: '1+ Uppercase letter', check: (val: string) => /[A-Z]/.test(val) },
            { id: 'special', text: '1+ Special characters', check: (val: string) => /[!@#$%^&*(),.?":{}|<>]/.test(val) },
            { id: 'number', text: '1+ Number', check: (val: string) => /[0-9]/.test(val) }
        ];

        // Create validation elements
        validations.forEach(validation => {
            const validationItem = document.createElement('div');
            validationItem.classList.add('validation-item');
            validationItem.id = `validation-${validation.id}`;

            const validationDot = document.createElement('span');
            validationDot.classList.add('validation-dot');
            validationDot.textContent = '•';

            const validationText = document.createElement('span');
            validationText.classList.add('validation-text');
            validationText.textContent = validation.text;

            validationItem.appendChild(validationDot);
            validationItem.appendChild(validationText);
            validationContainer.appendChild(validationItem);
        });

        // Add validation container after password container
        container.appendChild(passwordContainer);
        container.appendChild(validationContainer);

        // Add input event listener for validation
        input.addEventListener('input', () => {
            const value = input.value;

            validations.forEach(validation => {
                const validationElement = document.getElementById(`validation-${validation.id}`);
                if (validationElement) {
                    if (validation.check(value)) {
                        validationElement.classList.add('valid');
                    } else {
                        validationElement.classList.remove('valid');
                    }
                }
            });
        });

        return { container, input };
    }

    private renderSignInForm(): void {
        const authiqaDiv = this.initializeContainer();
        authiqaDiv.innerHTML = '';

        // Clear Google button rendering flags to ensure clean state
        (window as any)._authiqaGoogleButtonRendering = false;

        const title = document.createElement('h1');
        title.classList.add('authiqa-title');
        title.textContent = this.config.customization?.typography?.titleText?.signinText || 'Sign in';
        authiqaDiv.appendChild(title);

        // Always render the form first
        const form = document.createElement('form');
        form.classList.add('authiqa-form');
        form.style.display = 'flex';
        form.style.flexDirection = 'column';
        form.style.gap = '1rem';

        // Create email field with label
        const { container: emailContainer, input: email } = this.createLabeledInput(
            'email',
            'email',
            this.config.customization?.inputs?.emailPlaceholder || 'Email Address',
            this.config.customization?.inputs?.emailLabel || 'Email'
        );

        // Create password field with label
        const { container: passwordContainer, input: password } = this.createPasswordField(
            this.config.customization?.inputs?.passwordPlaceholder || 'Password',
            'password',
            this.config.customization?.inputs?.passwordLabel || 'Password'
        );

        // --- Forgot Password Link ---
        const resetPath = this.config.resetAuthPath || this.authUrls?.reset || '#';
        const navLinks = this.config.customization?.navLinks;
        const forgotPrompt = navLinks?.forgotPrompt || 'Forgot Password?';
        const forgotLinkText = navLinks?.forgotLinkText || 'Reset';
        const forgotDiv = document.createElement('div');
        forgotDiv.className = 'forgot-password';
        forgotDiv.innerHTML = `${forgotPrompt} <a href="${resetPath}">${forgotLinkText}</a>`;

        // Submit button with custom text
        const submit = document.createElement('button');
        submit.setAttribute('type', 'submit');
        submit.classList.add('authiqa-button');
        submit.textContent = this.config.customization?.buttons?.signinText || 'Sign In';
        submit.style.marginTop = '0.5rem';

        form.appendChild(emailContainer);
        form.appendChild(passwordContainer);
        form.appendChild(forgotDiv);
        form.appendChild(submit);

        form.addEventListener('submit', async (event) => {
            event.preventDefault();
            submit.setAttribute('data-original-text', submit.textContent || 'Submit');
            this.setLoadingState(submit, true, 'signin');
            const formData = {
                email: email.value,
                password: password.value,
                parentPublicKey: this.config.publicKey,
            };
            try {
                const response = await fetch(`${this.api.getApiBase()}/auth/signin`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });
                const result: SigninResponse = await response.json();
                switch (response.status) {
                    case 200:
                        if (result.success && 'data' in result) {
                            // Store tokens in session storage before handling password status
                            this.storeTokens(result.data.token);

                            // Store publicKey and email from user object (actual signin response format)
                            if (result.data.user && result.data.user.publicKey) {
                                sessionStorage.setItem('publicKey', result.data.user.publicKey);
                            }
                            if (result.data.user && result.data.user.email) {
                                sessionStorage.setItem('user_email', result.data.user.email);
                            }

                            // Store JWT secret if available
                            if (result.data.jwtSecret) {
                                localStorage.setItem('jwt_secret', result.data.jwtSecret);
                            }

                            if (result.data.passwordStatus?.expired) {
                                const resetUrl = this.config.resetAuthPath || this.authUrls?.reset || '';
                                this.showMessage(
                                    'Your password has expired. Please update it now.',
                                    'warning',
                                    resetUrl
                                );
                            } else if (result.data.passwordStatus?.daysUntilExpiry !== undefined &&
                                result.data.passwordStatus.daysUntilExpiry <= 14) {
                                const days = result.data.passwordStatus.daysUntilExpiry;
                                const resetUrl = this.config.resetAuthPath || this.authUrls?.reset || '';
                                const message = `Your password will expire in ${days} day${days !== 1 ? 's' : ''}. Please update it soon.`;
                                if (days <= 3) {
                                    this.showMessage(message, 'warning');
                                    setTimeout(() => {
                                        const successUrl = this.config.successAuthPath || this.authUrls?.successful || '';
                                        window.location.href = successUrl;
                                    }, 3000);
                                } else {
                                    const successUrl = this.config.successAuthPath || this.authUrls?.successful || '';
                                    this.showMessage(message, 'warning', successUrl);
                                }
                            } else {
                                const redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '';

                                // Dismiss Google One Tap before redirect to prevent persistence in SPAs
                                this.dismissGoogleOneTap();

                                this.showMessage(
                                    this.config.messages?.signinSuccess || 'Welcome back!',
                                    'success',
                                    redirectUrl
                                );
                            }
                        }
                        break;
                    default:
                        if (!result.success && 'error' in result) {
                            // Handle EMAIL_NOT_VERIFIED error based on organization settings
                            if (result.error.code === 'EMAIL_NOT_VERIFIED') {
                                if (this.emailVerificationRequired) {
                                    // Email verification is mandatory - show resend option
                                    const resendUrl = this.config.resendAuthPath || this.authUrls?.resend || '';
                                    this.showMessage(
                                        `${result.error.message} Please verify your email before signing in.`,
                                        'error',
                                        resendUrl
                                    );
                                } else {
                                    // Email verification is optional - allow signin anyway
                                    // This case should not normally occur if backend is configured correctly
                                    this.showMessage(result.error.message, 'error');
                                }
                            } else {
                                this.showMessage(result.error.message, 'error');
                            }
                        } else {
                            this.showMessage('An unexpected error occurred', 'error');
                        }
                }
            } catch (error) {
                console.error('Signin network error:', error);
                this.showMessage('Network error: Unable to connect to the server. Please check your connection and try again.', 'error');
            } finally {
                this.setLoadingState(submit, false, 'signin');
            }
        });

        // Insert the form into the container
        authiqaDiv.appendChild(form);

        // --- GitHub SSO Button (Dynamic Insert, styled, below main button) ---
        const maybeInsertGithubButton = () => {
            if (
                this.githubSsoConfig?.enabled &&
                this.githubSsoConfig.clientId &&
                !document.getElementById('github-button-container')
            ) {
                // Create GitHub button container
                const githubButtonContainer = document.createElement('div');
                githubButtonContainer.id = 'github-button-container';
                githubButtonContainer.style.margin = '0.5rem 0 0 0';
                githubButtonContainer.style.display = 'flex';
                githubButtonContainer.style.justifyContent = 'center';
                // Create the button
                const githubButton = document.createElement('button');
                githubButton.type = 'button';
                githubButton.className = 'authiqa-github-button';
                githubButton.style.background = '#ffffff !important';
                githubButton.style.color = '#000000 !important';
                githubButton.style.display = 'flex';
                githubButton.style.alignItems = 'center';
                githubButton.style.gap = '0.5rem';
                githubButton.style.fontWeight = 'bold';
                githubButton.style.fontSize = '1rem';
                githubButton.style.border = '1px solid #30363d';
                githubButton.style.padding = '0.5rem 1rem';
                githubButton.style.borderRadius = '4px';
                githubButton.style.width = '100%';
                githubButton.innerHTML = `<svg height="20" width="20" viewBox="0 0 16 16" fill="currentColor" style="margin-right: 0.5rem;"><path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.01.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.11.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.19 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"/></svg>Sign in with GitHub`;
                githubButton.onclick = () => {
                    // Build GitHub OAuth URL
                    const clientId = this.githubSsoConfig!.clientId;
                    // Always use signin path for GitHub OAuth (single callback URL limitation)
                    const redirectUri = this.config.signinAuthPath ?
                        (this.config.signinAuthPath.startsWith('http') ? this.config.signinAuthPath : window.location.origin + this.config.signinAuthPath) :
                        window.location.origin + window.location.pathname;
                    const scope = 'read:user user:email';
                    // Include source context in state for GitHub's single callback URL limitation
                    const stateData = {
                        source: 'signin',
                        random: Math.random().toString(36).substring(2, 15)
                    };
                    const state = encodeURIComponent(JSON.stringify(stateData));
                    sessionStorage.setItem('github_oauth_state', stateData.random);
                    const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${encodeURIComponent(clientId)}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scope)}&state=${encodeURIComponent(state)}`;
                    window.location.href = githubAuthUrl;
                };
                githubButtonContainer.appendChild(githubButton);
                // Insert below main submit button
                form.insertBefore(githubButtonContainer, submit.nextSibling);
            }
        };
        maybeInsertGithubButton();
        if (!this.githubSsoConfig?.enabled || !this.githubSsoConfig.clientId) {
            const interval = setInterval(() => {
                if (this.githubSsoConfig?.enabled && this.githubSsoConfig.clientId && !document.getElementById('github-button-container')) {
                    maybeInsertGithubButton();
                    clearInterval(interval);
                }
            }, 200);
            setTimeout(() => clearInterval(interval), 5000);
        }
        // --- End GitHub SSO Button ---

        // --- Google SSO Button (Dynamic Insert, styled, below GitHub button) ---
        const maybeInsertGoogleButton = () => {
            if (
                this.googleSsoConfig?.enabled &&
                this.googleSsoConfig.clientId &&
                !document.getElementById('google-button-container')
            ) {
                // Cancel any existing Google One Tap to prevent duplicates
                if ((window as any).google?.accounts?.id?.cancel) {
                    (window as any).google.accounts.id.cancel();
                }

                // Add flag to prevent multiple renders
                if ((window as any)._authiqaGoogleButtonRendering) {
                    return;
                }
                (window as any)._authiqaGoogleButtonRendering = true;

                if (!document.getElementById('google-identity-services')) {
                    const script = document.createElement('script');
                    script.src = 'https://accounts.google.com/gsi/client';
                    script.async = true;
                    script.defer = true;
                    script.id = 'google-identity-services';
                    document.head.appendChild(script);
                }
                if (typeof window._authiqaGoogleOneTapDismissed === 'undefined') {
                    window._authiqaGoogleOneTapDismissed = false;
                }
                if (typeof window._authiqaGoogleOneTapSuccessfulAuth === 'undefined') {
                    window._authiqaGoogleOneTapSuccessfulAuth = false;
                }
                const initializeGoogleServices = () => {
                    if ((window as any).google && (window as any).google.accounts) {
                        const handleGoogleLogin = async (response: any) => {
                            const idToken = response.credential;
                            if (!idToken) return;
                            const apiUrl = `${this.api.getApiBase()}/auth/google`;
                            try {
                                const res = await fetch(apiUrl, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        idToken,
                                        parentPublicKey: this.config.publicKey
                                    })
                                });
                                const responseText = await res.text();
                                let result;
                                try {
                                    result = JSON.parse(responseText);
                                } catch (parseError) {
                                    this.showMessage('Invalid response format from server', 'error');
                                    return;
                                }
                                if (res.status === 200 && result.success) {
                                    if (result.token) {
                                        this.storeTokens(result.token);
                                    }
                                    if (result.user && result.user.publicKey) {
                                        sessionStorage.setItem('publicKey', result.user.publicKey);
                                    }
                                    if (result.user && result.user.email) {
                                        sessionStorage.setItem('user_email', result.user.email);
                                    }
                                    if (result.jwtSecret) {
                                        localStorage.setItem('jwt_secret', result.jwtSecret);
                                    } else if (result.data && result.data.jwtSecret) {
                                        localStorage.setItem('jwt_secret', result.data.jwtSecret);
                                    }
                                    const redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '/';
                                    this.dismissGoogleOneTap();
                                    setTimeout(() => {
                                        window.location.href = redirectUrl;
                                    }, 150);
                                } else {
                                    this.showMessage(result.error?.message || 'Google sign-in failed', 'error');
                                }
                            } catch (err) {
                                this.showMessage('Network error during Google sign-in', 'error');
                            }
                        };
                        (window as any).google.accounts.id.initialize({
                            client_id: this.googleSsoConfig!.clientId,
                            callback: handleGoogleLogin,
                            ux_mode: 'popup',
                            auto_select: false,
                            use_fedcm_for_button: true,
                            context: 'signin',
                            button_auto_select: false
                        });
                        const googleButtonContainer = document.createElement('div');
                        googleButtonContainer.id = 'google-button-container';
                        googleButtonContainer.style.margin = '0.5rem 0 0 0';
                        googleButtonContainer.style.direction = 'ltr';
                        // Insert below GitHub button if present, else below submit
                        const githubBtn = document.getElementById('github-button-container');
                        if (githubBtn && githubBtn.parentNode) {
                            githubBtn.parentNode.insertBefore(googleButtonContainer, githubBtn.nextSibling);
                        } else {
                            form.insertBefore(googleButtonContainer, submit.nextSibling);
                        }
                        setTimeout(() => {
                            const containerWidth = googleButtonContainer.clientWidth;
                            (window as any).google.accounts.id.renderButton(
                                googleButtonContainer,
                                {
                                    theme: 'outline',
                                    size: 'large',
                                    text: 'continue_with',
                                    shape: 'rectangular',
                                    logo_alignment: 'left',
                                    width: containerWidth,
                                    auto_prompt: false,
                                    auto_select: false,
                                    type: 'standard'
                                }
                            );
                            setTimeout(() => {
                                const firstElement: any = document.querySelector(".nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb");
                                if (firstElement) {
                                    firstElement.style.display = "flex";
                                    firstElement.style.flexDirection = "row-reverse";
                                }
                                const secondElement: any = document.querySelector(".nsm7Bb-HzV7m-LgbsSe-Bz112c");
                                if (secondElement) {
                                    secondElement.style.marginRight = "10px";
                                }
                                // Clear the rendering flag after successful render
                                (window as any)._authiqaGoogleButtonRendering = false;
                            }, 200);
                        }, 0);
                        setTimeout(() => {
                            if (this.shouldEnableGoogleOneTap() && !window._authiqaGoogleOneTapDismissed && !this.hasGoogleOneTapSuccessfulAuth()) {
                                (window as any).google.accounts.id.prompt((notification: any) => {
                                    if (notification.isSkippedMoment()) {
                                        window._authiqaGoogleOneTapDismissed = true;
                                    } else if (notification.isDismissedMoment()) {
                                        window._authiqaGoogleOneTapDismissed = true;
                                        if (notification.getDismissedReason() === 'credential_returned') {
                                            this.markGoogleOneTapSuccessful();
                                        }
                                    }
                                });
                                window._authiqaGoogleOneTapDismissed = true;
                            }
                        }, 1500);
                    } else {
                        setTimeout(initializeGoogleServices, 100);
                    }
                };
                try {
                    initializeGoogleServices();
                } catch (error) {
                    console.error('Error initializing Google services:', error);
                    // Clear the rendering flag on error
                    (window as any)._authiqaGoogleButtonRendering = false;
                }
            }
        };
        maybeInsertGoogleButton();
        if (!this.googleSsoConfig?.enabled || !this.googleSsoConfig.clientId) {
            const interval = setInterval(() => {
                if (this.googleSsoConfig?.enabled && this.googleSsoConfig.clientId && !document.getElementById('google-button-container')) {
                    maybeInsertGoogleButton();
                    clearInterval(interval);
                }
            }, 200);
            setTimeout(() => clearInterval(interval), 5000);
        }
        // --- End Google SSO Button ---

        // --- GitHub OAuth callback handler (runs on every sign-in form render) ---
        (() => {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            const storedState = sessionStorage.getItem('github_oauth_state');

            // Parse state to get source and random token
            let stateData = null;
            try {
                stateData = state ? JSON.parse(decodeURIComponent(state)) : null;
            } catch (e) {
                // Fallback for old simple state format
                stateData = { source: 'signin', random: state };
            }

            if (code && state && storedState && stateData && stateData.random === storedState) {
                // Remove code and state from URL
                const url = new URL(window.location.href);
                url.searchParams.delete('code');
                url.searchParams.delete('state');
                window.history.replaceState({}, document.title, url.pathname + url.search);
                // Exchange code for token
                (async () => {
                    this.setLoadingState(submit, true, 'signin');
                    try {
                        const apiUrl = `${this.api.getApiBase()}/auth/github`;
                        const res = await fetch(apiUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                code,
                                parentPublicKey: this.config.publicKey
                            })
                        });
                        const responseText = await res.text();
                        let result;
                        try {
                            result = JSON.parse(responseText);
                        } catch (parseError) {
                            this.showMessage('Invalid response format from server', 'error');
                            return;
                        }
                        if (res.status === 200 && result.success) {
                            if (result.token) {
                                this.storeTokens(result.token);
                            }
                            if (result.user && result.user.publicKey) {
                                sessionStorage.setItem('publicKey', result.user.publicKey);
                            }
                            if (result.user && result.user.email) {
                                sessionStorage.setItem('user_email', result.user.email);
                            }
                            if (result.jwtSecret) {
                                localStorage.setItem('jwt_secret', result.jwtSecret);
                            } else if (result.data && result.data.jwtSecret) {
                                localStorage.setItem('jwt_secret', result.data.jwtSecret);
                            }
                            const redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '/';
                            this.showMessage(
                                this.config.messages?.signinSuccess || 'Welcome back!',
                                'success',
                                redirectUrl
                            );
                        } else {
                            this.showMessage(result.error?.message || 'GitHub sign-in failed', 'error');
                        }
                    } catch (err) {
                        this.showMessage('Network error during GitHub sign-in', 'error');
                    } finally {
                        this.setLoadingState(submit, false, 'signin');
                        sessionStorage.removeItem('github_oauth_state');
                    }
                })();
            }
        })();

        // --- Sign Up Navigation Link ---
        const signupPath = this.config.signupAuthPath || this.authUrls?.signup || '#';
        const signupPrompt = navLinks?.signupPrompt || "Don't have an account?";
        const signupLinkText = navLinks?.signupLinkText || 'Sign Up';
        const navDiv = document.createElement('div');
        navDiv.className = 'alternate-action';
        navDiv.innerHTML = `${signupPrompt} <a href="${signupPath}">${signupLinkText}</a>`;
        form.appendChild(navDiv);
    }

    private renderSignUpForm(): void {
        const authiqaDiv = this.initializeContainer();
        // Clear any existing content
        authiqaDiv.innerHTML = '';

        // Clear Google button rendering flags to ensure clean state
        (window as any)._authiqaGoogleSignupButtonRendering = false;

        const navLinks = this.config.customization?.navLinks;
        const title = document.createElement('h1');
        title.classList.add('authiqa-title'); // Add class
        // Apply custom title text if available
        title.textContent = this.config.customization?.typography?.titleText?.signupText || 'Sign up';
        authiqaDiv.appendChild(title);

        // Always render the form first
        const form = document.createElement('form');
        form.classList.add('authiqa-form'); // Add class
        form.style.display = 'flex';
        form.style.flexDirection = 'column';
        form.style.gap = '1rem'; /* Set gap to 1rem (about 16px) */

        // Username field with label
        const { container: usernameContainer, input: username } = this.createLabeledInput(
            'text',
            'username',
            this.config.customization?.inputs?.usernamePlaceholder || 'Username',
            this.config.customization?.inputs?.usernameLabel || 'Username'
        );

        // Email field with label
        const { container: emailContainer, input: email } = this.createLabeledInput(
            'email',
            'email',
            this.config.customization?.inputs?.emailPlaceholder || 'Email Address',
            this.config.customization?.inputs?.emailLabel || 'Email'
        );

        // Password field with label
        const { container: passwordContainer, input: password } = this.createPasswordField(
            this.config.customization?.inputs?.passwordPlaceholder || 'Password',
            'password',
            this.config.customization?.inputs?.passwordLabel || 'Password'
        );

        // Add to form
        form.appendChild(usernameContainer);
        form.appendChild(emailContainer);
        form.appendChild(passwordContainer);

        // Create terms checkbox container
        const termsContainer = document.createElement('div');
        termsContainer.classList.add('terms-container');
        termsContainer.style.display = 'flex';
        termsContainer.style.alignItems = 'flex-start';
        termsContainer.style.marginBottom = '1rem'; // Consistent margin

        const termsCheckbox = document.createElement('input');
        termsCheckbox.setAttribute('type', 'checkbox');
        termsCheckbox.setAttribute('id', 'terms');
        termsCheckbox.setAttribute('name', 'terms');
        termsCheckbox.setAttribute('required', 'required');
        termsCheckbox.style.marginTop = '0.25rem'; // Align with text
        termsCheckbox.style.marginRight = '0.5rem'; // Consistent margin

        const termsLabel = document.createElement('label');
        termsLabel.setAttribute('for', 'terms');
        termsLabel.style.flex = '1';
        termsLabel.style.margin = '0';
        termsLabel.style.padding = '0';
        termsLabel.style.color = '#525252';
        termsLabel.style.fontSize = '0.875rem';
        termsLabel.style.lineHeight = '1.4';

        const { agreePrefix, andConnector, defaultPrefix, linkText } =
            this.config.customization?.typography?.termsText || {
                agreePrefix: 'I agree with the',
                andConnector: 'and',
                defaultPrefix: 'default',
                linkText: {
                    terms: 'Terms of Service',
                    privacy: 'Privacy Policy',
                    notifications: 'Notification Settings'
                }
            };

        termsLabel.innerHTML = `${agreePrefix} <a href="${this.config.termsAndConditions || '#'}">${linkText.terms}</a> <a href="${this.config.privacy || '#'}">${linkText.privacy}</a> ${andConnector} ${defaultPrefix} <a href="${this.config.notificationSettings || '#'}">${linkText.notifications}</a>.`;

        termsContainer.appendChild(termsCheckbox);
        termsContainer.appendChild(termsLabel);

        // Submit button with custom text
        const submit = document.createElement('button');
        submit.setAttribute('type', 'submit');
        submit.classList.add('authiqa-button'); // Add class
        const buttonText = this.config.customization?.buttons?.signupText || 'Create Account';
        submit.textContent = buttonText;

        form.appendChild(termsContainer);
        form.appendChild(submit);

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            if (!termsCheckbox.checked) {
                this.showMessage('Please accept the terms and conditions', 'error');
                return;
            }

            // Add password validation before submission
            const passwordValidation = this.validatePassword(password.value);
            if (!passwordValidation.isValid && passwordValidation.error) {
                this.showMessage(`${passwordValidation.error.message} (${passwordValidation.error.code})`, 'error');
                return;
            }

            submit.setAttribute('data-original-text', submit.textContent || 'Submit');
            this.setLoadingState(submit, true, 'signup');

            const formData = {
                username: username.value,
                email: email.value,
                password: password.value,
                parentPublicKey: this.config.publicKey, // Changed from parentApiKey
                verifyAuthPath: this.config.verifyAuthPath
            };


            try {
                const response = await fetch(`${this.api.getApiBase()}/auth/signup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });

                const result = await response.json();


                switch (response.status) {
                    case 200:
                        if (result.success && result.data) {
                            // Store tokens - handle actual API response structure
                            if (result.token) {
                                this.storeTokens(result.token);
                            } else if (result.data && result.data.token) {
                                this.storeTokens(result.data.token);
                            }

                            // Store JWT secret - check both locations
                            if (result.jwtSecret) {
                                localStorage.setItem('jwt_secret', result.jwtSecret);
                            } else if (result.data && result.data.jwtSecret) {
                                localStorage.setItem('jwt_secret', result.data.jwtSecret);
                            }

                            // Store publicKey and email in session storage if available
                            if (result.data.data && result.data.data.publicKey) {
                                sessionStorage.setItem('publicKey', result.data.data.publicKey);
                            }
                            if (result.data.data && result.data.data.email) {
                                sessionStorage.setItem('user_email', result.data.data.email);
                            }

                            // Conditional redirection based on email verification requirement
                            let redirectUrl: string;
                            let message: string;

                            if (this.emailVerificationRequired) {
                                // Email verification is mandatory - redirect to resend page
                                redirectUrl = this.config.resendAuthPath || this.authUrls?.resend || '';
                                message = this.config.messages?.signupSuccess ||
                                    'Account created successfully! Please check your email to verify your account before signing in.';
                            } else {
                                // Email verification is optional - redirect to success page
                                redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '';
                                // Use a different message for optional verification - prioritize welcome message
                                message = this.config.messages?.signinSuccess || // Use signin success message for welcome
                                    'Successfully signed up! Welcome to your account.';
                            }

                            // Dismiss Google One Tap before redirect to prevent persistence in SPAs
                            this.dismissGoogleOneTap();

                            this.showMessage(message, 'success', redirectUrl);
                        }
                        break;

                    case 409:
                        // Handle conflict errors (email/username already exists)
                        const conflictError = result.error;
                        switch (conflictError.code) {
                            case 'EMAIL_ALREADY_EXISTS':
                                this.showMessage(`${conflictError.message} (${conflictError.code})`, 'error');
                                break;
                            case 'USERNAME_ALREADY_EXISTS':
                                this.showMessage(`${conflictError.message} (${conflictError.code})`, 'error');
                                break;
                            case 'DUPLICATE_EMAIL_USERNAME_COMBO':
                                this.showMessage(`${conflictError.message} (${conflictError.code})`, 'error');
                                break;
                            default:
                                this.showMessage(`${conflictError.message}`, 'error');
                        }
                        break;

                    case 400:
                        // Handle validation errors
                        const validationError = result.error;
                        switch (validationError.code) {
                            case 'MISSING_REQUEST_BODY':
                            case 'MISSING_REQUIRED_FIELDS':
                            case 'INVALID_EMAIL_FORMAT':
                            case 'INVALID_PASSWORD_FORMAT':
                            case 'INVALID_USERNAME_FORMAT':
                            case 'MISSING_PARENT_PUBLIC_KEY':
                                this.showMessage(`${validationError.message} (${validationError.code})`, 'error');
                                break;
                            default:
                                this.showMessage(`${validationError.message}`, 'error');
                        }
                        break;

                    case 401:
                        // Handle unauthorized errors
                        const unauthorizedError = result.error;
                        if (unauthorizedError.code === 'INVALID_PARENT_PUBLIC_KEY') {
                            this.showMessage(`${unauthorizedError.message} (${unauthorizedError.code})`, 'error');
                        } else {
                            this.showMessage(`${unauthorizedError.message}`, 'error');
                        }
                        break;

                    case 403:
                        // Handle forbidden errors
                        const forbiddenError = result.error;
                        if (forbiddenError.code === 'PARENT_ACCOUNT_INACTIVE') {
                            this.showMessage(`${forbiddenError.message} (${forbiddenError.code})`, 'error');
                        } else {
                            this.showMessage(`${forbiddenError.message}`, 'error');
                        }
                        break;

                    case 500:
                        this.showMessage('An internal server error occurred. Please try again later.', 'error');
                        break;

                    default:
                        this.showMessage('An unexpected error occurred. Please try again.', 'error');
                }
            } catch (error) {
                console.error('Signup network error:', error);
                this.showMessage('Network error: Unable to connect to the server. Please check your connection and try again.', 'error');
            } finally {
                this.setLoadingState(submit, false, 'signup');
            }
        });

        // Insert the form into the container
        authiqaDiv.appendChild(form);

        // --- GitHub SSO Button (Dynamic Insert, styled, below main button) ---
        const maybeInsertGithubButton = () => {
            if (
                this.githubSsoConfig?.enabled &&
                this.githubSsoConfig.clientId &&
                !document.getElementById('github-signup-button-container')
            ) {
                // Create GitHub button container
                const githubButtonContainer = document.createElement('div');
                githubButtonContainer.id = 'github-signup-button-container';
                githubButtonContainer.style.margin = '0.5rem 0 0 0';
                githubButtonContainer.style.display = 'flex';
                githubButtonContainer.style.justifyContent = 'center';
                // Create the button
                const githubButton = document.createElement('button');
                githubButton.type = 'button';
                
                githubButton.style.background = '#ffffff !important';
                githubButton.style.color = '#000000 !important';
                githubButton.style.display = 'flex';
                githubButton.style.alignItems = 'center';
                githubButton.style.gap = '0.5rem';
                githubButton.style.fontWeight = 'bold';
                githubButton.style.fontSize = '1rem';
                githubButton.style.border = '1px solid #30363d';
                githubButton.style.padding = '0.5rem 1rem';
                githubButton.style.borderRadius = '4px';
                githubButton.style.width = '100%';
                githubButton.innerHTML = `<svg height="20" width="20" viewBox="0 0 16 16" fill="currentColor" style="margin-right: 0.5rem;"><path d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.01.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.11.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.19 0 .21.15.46.55.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"/></svg>Sign up with GitHub`;
                githubButton.onclick = () => {
                    // Build GitHub OAuth URL
                    const clientId = this.githubSsoConfig!.clientId;
                    // Always use signin path for GitHub OAuth (single callback URL limitation)
                    const redirectUri = this.config.signinAuthPath ?
                        (this.config.signinAuthPath.startsWith('http') ? this.config.signinAuthPath : window.location.origin + this.config.signinAuthPath) :
                        window.location.origin + window.location.pathname;
                    const scope = 'read:user user:email';
                    // Include source context in state for GitHub's single callback URL limitation
                    const stateData = {
                        source: 'signup',
                        random: Math.random().toString(36).substring(2, 15)
                    };
                    const state = encodeURIComponent(JSON.stringify(stateData));
                    sessionStorage.setItem('github_oauth_state', stateData.random);
                    const githubAuthUrl = `https://github.com/login/oauth/authorize?client_id=${encodeURIComponent(clientId)}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scope)}&state=${encodeURIComponent(state)}`;
                    window.location.href = githubAuthUrl;
                };
                githubButtonContainer.appendChild(githubButton);
                // Insert below main submit button
                form.insertBefore(githubButtonContainer, submit.nextSibling);
            }
        };
        maybeInsertGithubButton();
        if (!this.githubSsoConfig?.enabled || !this.githubSsoConfig.clientId) {
            const interval = setInterval(() => {
                if (this.githubSsoConfig?.enabled && this.githubSsoConfig.clientId && !document.getElementById('github-signup-button-container')) {
                    maybeInsertGithubButton();
                    clearInterval(interval);
                }
            }, 200);
            setTimeout(() => clearInterval(interval), 5000);
        }
        // --- End GitHub SSO Button ---

        // --- Google SSO Button (Dynamic Insert, styled, below GitHub button) ---
        // Google SSO Button (Dynamic Insert, styled, below GitHub button)
        // Use a unique container id for signup
        const maybeInsertGoogleSignupButton = () => {
            if (
                this.googleSsoConfig?.enabled &&
                this.googleSsoConfig.clientId &&
                !document.getElementById('google-signup-button-container')
            ) {
                // Cancel any existing Google One Tap to prevent duplicates
                if ((window as any).google?.accounts?.id?.cancel) {
                    (window as any).google.accounts.id.cancel();
                }

                // Add flag to prevent multiple renders
                if ((window as any)._authiqaGoogleSignupButtonRendering) {
                    return;
                }
                (window as any)._authiqaGoogleSignupButtonRendering = true;

                if (!document.getElementById('google-identity-services')) {
                    const script = document.createElement('script');
                    script.src = 'https://accounts.google.com/gsi/client';
                    script.async = true;
                    script.defer = true;
                    script.id = 'google-identity-services';
                    document.head.appendChild(script);
                }
                if (typeof window._authiqaGoogleOneTapDismissed === 'undefined') {
                    window._authiqaGoogleOneTapDismissed = false;
                }
                if (typeof window._authiqaGoogleOneTapSuccessfulAuth === 'undefined') {
                    window._authiqaGoogleOneTapSuccessfulAuth = false;
                }
                const initializeGoogleServicesSignup = () => {
                    if ((window as any).google && (window as any).google.accounts) {
                        const handleGoogleSignup = async (response: any) => {
                            const idToken = response.credential;
                            if (!idToken) return;
                            const apiUrl = `${this.api.getApiBase()}/auth/google`;
                            try {
                                const res = await fetch(apiUrl, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        idToken,
                                        parentPublicKey: this.config.publicKey
                                    })
                                });
                                const responseText = await res.text();
                                let result;
                                try {
                                    result = JSON.parse(responseText);
                                } catch (parseError) {
                                    this.showMessage('Invalid response format from server', 'error');
                                    return;
                                }
                                if (res.status === 200 && result.success) {
                                    if (result.token) {
                                        this.storeTokens(result.token);
                                    }
                                    if (result.user && result.user.publicKey) {
                                        sessionStorage.setItem('publicKey', result.user.publicKey);
                                    }
                                    if (result.user && result.user.email) {
                                        sessionStorage.setItem('user_email', result.user.email);
                                    }
                                    if (result.jwtSecret) {
                                        localStorage.setItem('jwt_secret', result.jwtSecret);
                                    } else if (result.data && result.data.jwtSecret) {
                                        localStorage.setItem('jwt_secret', result.data.jwtSecret);
                                    }
                                    const redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '/';
                                    this.dismissGoogleOneTap();
                                    setTimeout(() => {
                                        window.location.href = redirectUrl;
                                    }, 150);
                                } else {
                                    this.showMessage(result.error?.message || 'Google sign-up failed', 'error');
                                }
                            } catch (err) {
                                this.showMessage('Network error during Google sign-up', 'error');
                            }
                        };
                        (window as any).google.accounts.id.initialize({
                            client_id: this.googleSsoConfig!.clientId,
                            callback: handleGoogleSignup,
                            ux_mode: 'popup',
                            auto_select: false,
                            use_fedcm_for_button: true,
                            context: 'signup',
                            button_auto_select: false
                        });
                        const googleButtonContainer = document.createElement('div');
                        googleButtonContainer.id = 'google-signup-button-container';
                        googleButtonContainer.style.margin = '0.5rem 0 0 0';
                        googleButtonContainer.style.direction = 'ltr';
                        // Insert below GitHub button if present, else below submit
                        const githubBtn = document.getElementById('github-signup-button-container');
                        if (githubBtn && githubBtn.parentNode) {
                            githubBtn.parentNode.insertBefore(googleButtonContainer, githubBtn.nextSibling);
                        } else {
                            form.insertBefore(googleButtonContainer, submit.nextSibling);
                        }
                        setTimeout(() => {
                            const containerWidth = googleButtonContainer.clientWidth;
                            (window as any).google.accounts.id.renderButton(
                                googleButtonContainer,
                                {
                                    theme: 'outline',
                                    size: 'large',
                                    text: 'continue_with',
                                    shape: 'rectangular',
                                    logo_alignment: 'left',
                                    width: containerWidth,
                                    auto_prompt: false,
                                    auto_select: false,
                                    type: 'standard'
                                }
                            );
                            setTimeout(() => {
                                const firstElement: any = document.querySelector('.nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb');
                                if (firstElement) {
                                    firstElement.style.display = 'flex';
                                    firstElement.style.flexDirection = 'row-reverse';
                                }
                                const secondElement: any = document.querySelector('.nsm7Bb-HzV7m-LgbsSe-Bz112c');
                                if (secondElement) {
                                    secondElement.style.marginRight = '10px';
                                }
                                // Clear the rendering flag after successful render
                                (window as any)._authiqaGoogleSignupButtonRendering = false;
                            }, 200);
                        }, 0);
                        setTimeout(() => {
                            if (this.shouldEnableGoogleOneTap() && !window._authiqaGoogleOneTapDismissed && !this.hasGoogleOneTapSuccessfulAuth()) {
                                (window as any).google.accounts.id.prompt((notification: any) => {
                                    if (notification.isSkippedMoment()) {
                                        window._authiqaGoogleOneTapDismissed = true;
                                    } else if (notification.isDismissedMoment()) {
                                        window._authiqaGoogleOneTapDismissed = true;
                                        if (notification.getDismissedReason() === 'credential_returned') {
                                            this.markGoogleOneTapSuccessful();
                                        }
                                    }
                                });
                                window._authiqaGoogleOneTapDismissed = true;
                            }
                        }, 1500);
                    } else {
                        setTimeout(initializeGoogleServicesSignup, 100);
                    }
                };
                try {
                    initializeGoogleServicesSignup();
                } catch (error) {
                    console.error('Error initializing Google signup services:', error);
                    // Clear the rendering flag on error
                    (window as any)._authiqaGoogleSignupButtonRendering = false;
                }
            }
        };
        maybeInsertGoogleSignupButton();
        if (!this.googleSsoConfig?.enabled || !this.googleSsoConfig.clientId) {
            const interval = setInterval(() => {
                if (this.googleSsoConfig?.enabled && this.googleSsoConfig.clientId && !document.getElementById('google-signup-button-container')) {
                    maybeInsertGoogleSignupButton();
                    clearInterval(interval);
                }
            }, 200);
            setTimeout(() => clearInterval(interval), 5000);
        }
        // --- End Google SSO Button ---

        // --- GitHub OAuth callback handler (runs on every signup form render) ---
        (() => {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const state = urlParams.get('state');
            const storedState = sessionStorage.getItem('github_oauth_state');

            // Parse state to get source and random token
            let stateData = null;
            try {
                stateData = state ? JSON.parse(decodeURIComponent(state)) : null;
            } catch (e) {
                // Fallback for old simple state format
                stateData = { source: 'signup', random: state };
            }

            if (code && state && storedState && stateData && stateData.random === storedState) {
                // Remove code and state from URL
                const url = new URL(window.location.href);
                url.searchParams.delete('code');
                url.searchParams.delete('state');
                window.history.replaceState({}, document.title, url.pathname + url.search);
                // Exchange code for token
                (async () => {
                    this.setLoadingState(submit, true, 'signup');
                    try {
                        const apiUrl = `${this.api.getApiBase()}/auth/github`;
                        const res = await fetch(apiUrl, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                code,
                                parentPublicKey: this.config.publicKey
                            })
                        });
                        const responseText = await res.text();
                        let result;
                        try {
                            result = JSON.parse(responseText);
                        } catch (parseError) {
                            this.showMessage('Invalid response format from server', 'error');
                            return;
                        }
                        if (res.status === 200 && result.success) {
                            if (result.token) {
                                this.storeTokens(result.token);
                            }
                            if (result.user && result.user.publicKey) {
                                sessionStorage.setItem('publicKey', result.user.publicKey);
                            }
                            if (result.user && result.user.email) {
                                sessionStorage.setItem('user_email', result.user.email);
                            }
                            if (result.jwtSecret) {
                                localStorage.setItem('jwt_secret', result.jwtSecret);
                            } else if (result.data && result.data.jwtSecret) {
                                localStorage.setItem('jwt_secret', result.data.jwtSecret);
                            }
                            const redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '/';
                            this.showMessage(
                                this.config.messages?.signupSuccess || 'Account created successfully!',
                                'success',
                                redirectUrl
                            );
                        } else {
                            this.showMessage(result.error?.message || 'GitHub sign-up failed', 'error');
                        }
                    } catch (err) {
                        this.showMessage('Network error during GitHub sign-up', 'error');
                    } finally {
                        this.setLoadingState(submit, false, 'signup');
                        sessionStorage.removeItem('github_oauth_state');
                    }
                })();
            }
        })();

        // --- Sign In Navigation Link ---
        const signinPath = this.config.signinAuthPath || this.authUrls?.signin || '#';
        const signinPrompt = navLinks?.signinPrompt || 'Already have an account?';
        const signinLinkText = navLinks?.signinLinkText || 'Sign In';
        const navDiv = document.createElement('div');
        navDiv.className = 'alternate-action';
        navDiv.innerHTML = `${signinPrompt} <a href="${signinPath}">${signinLinkText}</a>`;
        form.appendChild(navDiv);
    }

    private validatePassword(password: string): { isValid: boolean; error?: { code: string; message: string } } {
        // Password must contain:
        // 1. At least 8 characters
        // 2. At least one uppercase letter
        // 3. At least one number
        // 4. At least one special character
        const hasMinLength = password.length >= 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasNumber = /[0-9]/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

        if (!hasMinLength || !hasUpperCase || !hasNumber || !hasSpecialChar) {
            let message = 'Password must contain:';
            if (!hasMinLength) message += ' at least 8 characters,';
            if (!hasUpperCase) message += ' at least one uppercase letter,';
            if (!hasNumber) message += ' at least one number,';
            if (!hasSpecialChar) message += ' at least one special character,';

            // Remove trailing comma and add period
            message = message.replace(/,$/, '');

            return {
                isValid: false,
                error: {
                    code: 'INVALID_PASSWORD_FORMAT',
                    message: message
                }
            };
        }

        return { isValid: true };
    }

    private renderResetPasswordForm(): void {
        const authiqaDiv = this.initializeContainer();
        authiqaDiv.innerHTML = '';

        const title = document.createElement('h1');
        title.classList.add('authiqa-title'); // Add class
        // Apply custom title text
        title.textContent = this.config.customization?.typography?.titleText?.resetText || 'Reset Password';

        // Add subtitle text if available
        if (this.config.customization?.typography?.subtitleText?.resetText) {
            title.setAttribute('data-subtitle', this.config.customization.typography.subtitleText.resetText);
        }

        authiqaDiv.appendChild(title);

        const form = document.createElement('form');
        form.classList.add('authiqa-form'); // Add class
        form.style.display = 'flex';
        form.style.flexDirection = 'column';
        form.style.gap = '1rem'; /* Set gap to 1rem (about 16px) */

        // Email field with label
        const { container: emailContainer, input: email } = this.createLabeledInput(
            'email',
            'email',
            this.config.customization?.inputs?.emailPlaceholder || 'Email Address',
            this.config.customization?.inputs?.emailLabel || 'Email'
        );

        // Add to form
        form.appendChild(emailContainer);

        // Submit button with custom text
        const submit = document.createElement('button');
        submit.setAttribute('type', 'submit');
        submit.classList.add('authiqa-button'); // Add class
        submit.textContent = this.config.customization?.buttons?.resetText || 'Reset Password';

        form.appendChild(submit);

        // --- Navigation Link to Sign In (alternate-action) ---
        const signinPath = this.config.signinAuthPath || this.authUrls?.signin || '#';
        const navLinks = this.config.customization?.navLinks;
        const backToSigninPrompt = navLinks?.backToSigninPrompt || 'Back to Sign In?';
        const navDiv = document.createElement('div');
        navDiv.className = 'alternate-action';
        navDiv.innerHTML = `${backToSigninPrompt} <a href="${signinPath}">Sign In</a>`;
        form.appendChild(navDiv);

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            // Add loading state
            this.setLoadingState(submit, true, 'reset');

            const formData = {
                email: email.value,
                parentPublicKey: this.config.publicKey, // Changed from parentApiKey
                updatePasswordPath: this.config.updatePasswordPath
            };

            try {
                const response = await fetch(`${this.api.getApiBase()}/auth/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });

                const result: ApiResponse<{ message: string }> = await response.json();

                switch (response.status) {
                    case 200:
                        if (result.success && result.data) {
                            this.showMessage(
                                this.config.messages?.resetSuccess || result.data.message,
                                'success'
                            );
                        }
                        break;

                    default:
                        if (!result.success && result.error) {
                            this.showMessage(result.error.message, 'error');
                        } else {
                            this.showMessage('An unexpected error occurred', 'error');
                        }
                }
            } catch (error) {
                console.error('Reset password network error:', error);
                this.showMessage('Unable to connect to the server', 'error');
            } finally {
                // Reset loading state
                this.setLoadingState(submit, false, 'reset');
            }
        });

        authiqaDiv.appendChild(form);
    }

    private renderUpdatePasswordForm(): void {
        const authiqaDiv = this.initializeContainer();
        authiqaDiv.innerHTML = '';

        const title = document.createElement('h1');
        title.classList.add('authiqa-title'); // Add class
        // Apply custom title text
        title.textContent = this.config.customization?.typography?.titleText?.updateText || 'Update Password';
        authiqaDiv.appendChild(title);

        // Get token from URL first
        const urlParams = new URLSearchParams(window.location.search);
        const tokenFromUrl = urlParams.get('token');

        const form = document.createElement('form');
        form.classList.add('authiqa-form'); // Add class
        form.style.display = 'flex';
        form.style.flexDirection = 'column';
        form.style.gap = '1rem'; /* Set gap to 1rem (about 16px) */

        // Create hidden token input if URL token exists
        if (tokenFromUrl) {
            const hiddenToken = document.createElement('input');
            hiddenToken.setAttribute('type', 'hidden');
            hiddenToken.setAttribute('name', 'token');
            hiddenToken.value = tokenFromUrl;
            form.appendChild(hiddenToken);
        } else {
            console.warn('No token found in URL - password reset may fail');
        }

        // Create new password field WITHOUT validation indicators
        const newPasswordContainer = document.createElement('div');
        newPasswordContainer.classList.add('authiqa-labeled-input');

        // Create label
        const newPasswordLabel = document.createElement('label');
        newPasswordLabel.setAttribute('for', 'authiqa-newPassword');
        newPasswordLabel.textContent = this.config.customization?.inputs?.passwordLabel || 'New Password';
        newPasswordLabel.classList.add('authiqa-label');
        newPasswordContainer.appendChild(newPasswordLabel);

        // Create password container
        const passwordFieldContainer = document.createElement('div');
        passwordFieldContainer.className = 'password-field-container';
        passwordFieldContainer.classList.add('authiqa-password-container');

        // Create input
        const newPassword = document.createElement('input');
        newPassword.setAttribute('type', 'password');
        newPassword.setAttribute('id', 'authiqa-newPassword');
        newPassword.setAttribute('name', 'newPassword');
        newPassword.setAttribute('placeholder', this.config.customization?.inputs?.passwordPlaceholder || 'New Password');
        newPassword.setAttribute('required', 'true');
        newPassword.setAttribute('minlength', '6');
        newPassword.classList.add('authiqa-input');

        // Add input to password container
        passwordFieldContainer.appendChild(newPassword);

        // Create toggle button
        const toggleButton = document.createElement('button');
        toggleButton.setAttribute('type', 'button');
        toggleButton.classList.add('password-toggle');
        toggleButton.innerHTML = '👁️';

        toggleButton.addEventListener('click', () => {
            const type = newPassword.getAttribute('type');
            newPassword.setAttribute('type', type === 'password' ? 'text' : 'password');
            toggleButton.innerHTML = type === 'password' ? '👁️‍🗨️' : '👁️';
        });

        // Add toggle to password container
        passwordFieldContainer.appendChild(toggleButton);
        newPasswordContainer.appendChild(passwordFieldContainer);

        // Confirm password field with label
        const confirmPasswordContainer = document.createElement('div');
        confirmPasswordContainer.classList.add('authiqa-labeled-input');

        // Create label
        const confirmPasswordLabel = document.createElement('label');
        confirmPasswordLabel.setAttribute('for', 'authiqa-confirmPassword');
        confirmPasswordLabel.textContent = this.config.customization?.inputs?.confirmPasswordLabel || 'Confirm Password';
        confirmPasswordLabel.classList.add('authiqa-label');
        confirmPasswordContainer.appendChild(confirmPasswordLabel);

        // Create password container
        const confirmPasswordFieldContainer = document.createElement('div');
        confirmPasswordFieldContainer.className = 'password-field-container';
        confirmPasswordFieldContainer.classList.add('authiqa-password-container');

        // Create input
        const confirmPassword = document.createElement('input');
        confirmPassword.setAttribute('type', 'password');
        confirmPassword.setAttribute('id', 'authiqa-confirmPassword');
        confirmPassword.setAttribute('name', 'confirmPassword');
        confirmPassword.setAttribute('placeholder', this.config.customization?.inputs?.confirmPasswordPlaceholder || 'Confirm Password');
        confirmPassword.setAttribute('required', 'true');
        confirmPassword.classList.add('authiqa-input');

        // Add input to password container
        confirmPasswordFieldContainer.appendChild(confirmPassword);

        // Create toggle button for confirm password
        const confirmToggleButton = document.createElement('button');
        confirmToggleButton.setAttribute('type', 'button');
        confirmToggleButton.classList.add('password-toggle');
        confirmToggleButton.innerHTML = '👁️';

        confirmToggleButton.addEventListener('click', () => {
            const type = confirmPassword.getAttribute('type');
            confirmPassword.setAttribute('type', type === 'password' ? 'text' : 'password');
            confirmToggleButton.innerHTML = type === 'password' ? '👁️‍🗨️' : '👁️';
        });

        // Add toggle to password container
        confirmPasswordFieldContainer.appendChild(confirmToggleButton);
        confirmPasswordContainer.appendChild(confirmPasswordFieldContainer);

        // Create validation container
        const validationContainer = document.createElement('div');
        validationContainer.classList.add('password-validation-container');

        // Create validation items
        const validations = [
            { id: 'length', text: '8+ Characters long', check: (val: string) => val.length >= 8 },
            { id: 'uppercase', text: '1+ Uppercase letter', check: (val: string) => /[A-Z]/.test(val) },
            { id: 'special', text: '1+ Special characters', check: (val: string) => /[!@#$%^&*(),.?":{}|<>]/.test(val) },
            { id: 'number', text: '1+ Number', check: (val: string) => /[0-9]/.test(val) }
        ];

        // Create validation elements
        validations.forEach(validation => {
            const validationItem = document.createElement('div');
            validationItem.classList.add('validation-item');
            validationItem.id = `validation-${validation.id}`;

            const validationDot = document.createElement('span');
            validationDot.classList.add('validation-dot');
            validationDot.textContent = '•';

            const validationText = document.createElement('span');
            validationText.classList.add('validation-text');
            validationText.textContent = validation.text;

            validationItem.appendChild(validationDot);
            validationItem.appendChild(validationText);
            validationContainer.appendChild(validationItem);
        });

        // Add to form
        form.appendChild(newPasswordContainer);
        form.appendChild(confirmPasswordContainer);
        form.appendChild(validationContainer); // Add validation container after both password fields

        // Add input event listener for validation
        newPassword.addEventListener('input', () => {
            const value = newPassword.value;

            validations.forEach(validation => {
                const validationElement = document.getElementById(`validation-${validation.id}`);
                if (validationElement) {
                    if (validation.check(value)) {
                        validationElement.classList.add('valid');
                    } else {
                        validationElement.classList.remove('valid');
                    }
                }
            });
        });

        // Add real-time password matching validation
        const validatePasswords = () => {
            if (confirmPassword.value) {
                if (newPassword.value !== confirmPassword.value) {
                    confirmPassword.setCustomValidity('Passwords do not match');
                } else {
                    confirmPassword.setCustomValidity('');
                }
            }
        };

        newPassword.addEventListener('input', validatePasswords);
        confirmPassword.addEventListener('input', validatePasswords);

        // Submit button with custom text
        const submit = document.createElement('button');
        submit.setAttribute('type', 'submit');
        submit.classList.add('authiqa-button'); // Add class
        submit.textContent = this.config.customization?.buttons?.updateText || 'Update Password';

        form.appendChild(submit);

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            // Frontend validation
            if (newPassword.value !== confirmPassword.value) {
                this.showMessage('Passwords do not match', 'error');
                return;
            }

            this.setLoadingState(submit, true, 'update');

            const formData = {
                token: tokenFromUrl,
                password: newPassword.value
            };

            try {
                const response = await fetch(`${this.api.getApiBase()}/auth/update-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData),
                });

                const result = await response.json();

                switch (response.status) {
                    case 200:
                        if (result.success && result.data) {
                            const redirectUrl = this.config.signinAuthPath || this.authUrls?.signin;
                            this.showMessage(
                                this.config.messages?.updateSuccess || 'Password updated successfully!',
                                'success',
                                redirectUrl
                            );
                        }
                        break;

                    default:
                        // Show original error message from server
                        if (!result.success && result.error) {
                            this.showMessage(result.error.message, 'error');
                        } else {
                            this.showMessage('An unexpected error occurred', 'error');
                        }
                }
            } catch (error) {
                this.showMessage('Network error: Unable to connect to the server', 'error');
            } finally {
                this.setLoadingState(submit, false, 'update');
            }
        });

        authiqaDiv.appendChild(form);
    }

    private renderResendConfirmationForm(): void {
        const authiqaDiv = this.initializeContainer();
        authiqaDiv.innerHTML = '';

        // Check if user is authenticated to determine rendering mode
        const isAuthenticated = this.isUserAuthenticated();

        if (isAuthenticated) {
            // Mode 2: Authenticated - Simple button only (for success/dashboard pages)
            this.renderSimpleVerifyButton(authiqaDiv);
        } else {
            // Mode 1: Traditional - Full form (for resend pages)
            this.renderTraditionalResendForm(authiqaDiv);
        }
    }

    private renderSimpleVerifyButton(container: HTMLElement): void {
        // Simple button for authenticated users (success/dashboard pages)
        const button = document.createElement('button');
        button.classList.add('authiqa-button');
        button.textContent = this.config.customization?.buttons?.resendText || 'Verify Email';
        button.style.width = '100%';
        button.style.maxWidth = '300px';
        button.style.margin = '0 auto';
        button.style.display = 'block';

        this.attachResendButtonHandler(button, true); // true = authenticated mode
        container.appendChild(button);
    }

    private renderTraditionalResendForm(container: HTMLElement): void {
        // Traditional form for non-authenticated users (resend pages)
        const title = document.createElement('h1');
        title.classList.add('authiqa-title');
        title.textContent = this.config.customization?.typography?.titleText?.resendText || 'Resend Confirmation';
        container.appendChild(title);

        const form = document.createElement('form');
        form.classList.add('authiqa-form');
        form.style.display = 'flex';
        form.style.flexDirection = 'column';
        form.style.gap = '1rem';

        // Get email from URL if available
        const urlParams = new URLSearchParams(window.location.search);
        const emailFromUrl = urlParams.get('email');

        const emailField = this.createLabeledInput(
            'email',
            'email',
            this.config.customization?.inputs?.emailPlaceholder || 'Email Address',
            this.config.customization?.inputs?.emailLabel || 'Email'
        );
        const emailContainer = emailField.container;
        const email = emailField.input;

        // Set email value if available from URL
        if (emailFromUrl) {
            email.value = emailFromUrl;
        }

        form.appendChild(emailContainer);

        // Submit button
        const submit = document.createElement('button');
        submit.setAttribute('type', 'submit');
        submit.classList.add('authiqa-button');
        submit.textContent = this.config.customization?.buttons?.resendText || 'Verify Email';
        form.appendChild(submit);

        // Navigation link back to signin
        const signinPath = this.config.signinAuthPath || this.authUrls?.signin || '#';
        const navLinks = this.config.customization?.navLinks;
        const backToSigninPrompt = navLinks?.backToSigninPrompt || 'Back to Sign In?';
        const navDiv = document.createElement('div');
        navDiv.className = 'alternate-action';
        navDiv.innerHTML = `${backToSigninPrompt} <a href="${signinPath}">Sign In</a>`;
        form.appendChild(navDiv);

        // Attach form handler
        form.addEventListener('submit', async (event) => {
            event.preventDefault();
            this.handleTraditionalResendSubmit(email, submit);
        });

        container.appendChild(form);
    }

    private attachResendButtonHandler(button: HTMLButtonElement, isAuthenticated: boolean): void {
        button.addEventListener('click', async (event) => {
            event.preventDefault();

            // Implement rate limiting for authenticated users
            if (isAuthenticated) {
                const lastResendTime = localStorage.getItem('authiqa_last_resend_time');
                const now = Date.now();
                const cooldownPeriod = 60000; // 60 seconds

                if (lastResendTime && (now - parseInt(lastResendTime)) < cooldownPeriod) {
                    const remainingTime = Math.ceil((cooldownPeriod - (now - parseInt(lastResendTime))) / 1000);
                    this.showMessage(`Please wait ${remainingTime} seconds before requesting another email.`, 'error');
                    return;
                }
            }

            this.setLoadingState(button, true, 'resend');

            try {
                // For authenticated users, get email from stored user data
                const token = this.getStoredToken();
                if (!token) {
                    this.showMessage('Authentication token not found. Please sign in again.', 'error');
                    return;
                }

                const response = await fetch(`${this.api.getApiBase()}/auth/request-new-confirmation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        verifyAuthPath: this.config.verifyAuthPath
                    }),
                });

                const result = await response.json();

                if (response.status === 200 && result.success) {
                    this.showMessage('Verification email sent successfully!', 'success');
                    // Set rate limiting timestamp
                    localStorage.setItem('authiqa_last_resend_time', Date.now().toString());
                } else {
                    throw new Error(result.error?.message || 'Failed to send verification email');
                }
            } catch (error: any) {
                this.showMessage(error.message || 'An error occurred', 'error');
            } finally {
                this.setLoadingState(button, false, 'resend');
            }
        });
    }

    private async handleTraditionalResendSubmit(email: HTMLInputElement, submit: HTMLButtonElement): Promise<void> {
        if (!email || !email.value) {
            this.showMessage('Please enter your email address.', 'error');
            return;
        }

        this.setLoadingState(submit, true, 'resend');

        try {
            const formData = {
                email: email.value,
                parentPublicKey: this.config.publicKey,
                verifyAuthPath: this.config.verifyAuthPath
            };

            const response = await fetch(`${this.api.getApiBase()}/auth/request-new-confirmation`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData),
            });

            const result = await response.json();

            if (response.status === 200 && result.success) {
                this.showMessage(
                    this.config.messages?.resendSuccess || result.data?.message || 'Verification email sent successfully!',
                    'success'
                );
            } else {
                throw new Error(result.error?.message || 'Failed to send verification email');
            }
        } catch (error: any) {
            this.showMessage(error.message || 'Network error: Unable to connect to the server', 'error');
        } finally {
            this.setLoadingState(submit, false, 'resend');
        }
    }

    private renderVerificationStatus(status: 'loading' | 'success' | 'error', message: string): void {
        const authiqaDiv = this.initializeContainer();
        authiqaDiv.innerHTML = '';

        const container = document.createElement('div');
        container.className = 'verification-status';

        const title = document.createElement('h1');
        title.textContent = 'Email Verification';

        const statusContainer = document.createElement('div');

        if (status === 'loading') {
            const loader = document.createElement('div');
            loader.className = 'verification-loader';
            statusContainer.appendChild(loader);
            message = this.config.messages?.verificationLoading || message;
        } else {
            const icon = document.createElement('div');
            icon.className = `verification-icon ${status}`;
            icon.innerHTML = status === 'success' ? '✓' : '✕';
            statusContainer.appendChild(icon);

            // Only use custom message for success, not for error
            if (status === 'success') {
                message = this.config.messages?.verificationSuccess || message;
            }
            // For error, use the original message
        }

        const messageElement = document.createElement('p');
        messageElement.textContent = message;

        container.appendChild(title);
        container.appendChild(statusContainer);
        container.appendChild(messageElement);
        authiqaDiv.appendChild(container);
    }

    private async handleEmailVerification(): Promise<void> {
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');

        if (!token) {
            this.renderVerificationStatus('error', 'Invalid verification token (INVALID_TOKEN)');
            return;
        }

        // Show loading state
        this.renderVerificationStatus('loading', this.config.messages?.verificationLoading || 'Verifying your email address...');

        try {
            const response = await fetch(`${this.api.getApiBase()}/auth/confirm-email?token=${encodeURIComponent(token)}`, {
                headers: {
                    'X-Public-Key': this.config.publicKey,
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            switch (response.status) {
                case 200:
                    if (result.success && result.data) {
                        this.renderVerificationStatus('success',
                            this.config.messages?.verificationSuccess || result.data.message || 'Email verified successfully'
                        );

                        // Conditional redirection based on email verification requirement
                        let redirectUrl: string;

                        if (this.emailVerificationRequired) {
                            // Required verification: User must sign in again after verifying email
                            redirectUrl = this.config.signinAuthPath || this.authUrls?.signin || '/';
                        } else {
                            // Optional verification: User is already authenticated, continue to success page
                            redirectUrl = this.config.successAuthPath || this.authUrls?.successful || '/';
                        }

                        setTimeout(() => {
                            window.location.href = redirectUrl;
                        }, 2000);
                    }
                    break;

                case 400:
                    const badRequestError = result.error;
                    this.renderVerificationStatus('error', `${badRequestError.message} (${badRequestError.code})`);
                    break;

                case 404:
                    const notFoundError = result.error;
                    this.renderVerificationStatus('error', `${notFoundError.message} (${notFoundError.code})`);
                    break;

                case 500:
                    const serverError = result.error;
                    this.renderVerificationStatus('error', `${serverError.message} (${serverError.code})`);
                    break;

                default:
                    this.renderVerificationStatus('error', 'An unexpected error occurred. Please try again.');
            }
        } catch (error) {
            console.error('Error during email verification:', error);
            this.renderVerificationStatus('error',
                'Network error: Unable to connect to the server. Please check your connection and try again.'
            );
        }
    }

    private showMessage(displayMessage: string, type: 'success' | 'error' | 'warning', redirect?: string): void {
        const messageElement = document.createElement('div');
        messageElement.classList.add('authiqa-message');
        messageElement.classList.add(`authiqa-message-${type}`);

        // Set different colors based on message type
        if (type === 'success') {
            messageElement.style.backgroundColor = '#4caf50';
        } else if (type === 'error') {
            messageElement.style.backgroundColor = '#f44336';
        } else if (type === 'warning') {
            messageElement.style.backgroundColor = '#ff9800';
        }

        // Handle redirect logic - only if no specific redirect URL was provided
        let redirectUrl = redirect;
        if (type === 'success' && !redirect) {  // Only provide fallback if no redirect was specified
            switch (this.currentAction) {
                case 'signin':
                    redirectUrl = this.config.successAuthPath || this.authUrls?.successful;
                    break;
                case 'update':
                    redirectUrl = this.config.signinAuthPath || this.authUrls?.signin;
                    break;
                case 'signup':
                    redirectUrl = this.config.successAuthPath || this.authUrls?.successful; // Changed from resendAuthPath
                    break;
            }
        }

        const existingMessage = document.querySelector('.authiqa-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        messageElement.textContent = displayMessage;

        document.body.appendChild(messageElement);
        messageElement.classList.add('show');

        // Debug log for message

        // Increase durations: error 7000ms, success 4000ms, warning 5000ms
        let duration = 2000;
        if (type === 'error') duration = 7000;
        else if (type === 'success') duration = 4000;
        else if (type === 'warning') duration = 5000;

        setTimeout(() => {
            messageElement.classList.remove('show');
            setTimeout(() => {
                messageElement.remove();
                if (redirectUrl) {
                    window.location.href = redirectUrl;
                }
            }, 300);
        }, duration);
    }

    private getCustomSuccessMessage(defaultMessage: string): string {
        const action = this.currentAction;
        switch (action) {
            case 'signin':
                return this.config.messages?.signinSuccess || defaultMessage;
            case 'signup':
                return this.config.messages?.signupSuccess || 'Successfully signed up! Welcome to your account.'; // Updated default message
            case 'reset':
                return this.config.messages?.resetSuccess || defaultMessage;
            case 'update':
                return this.config.messages?.updateSuccess || defaultMessage;
            case 'resend':
                return this.config.messages?.resendSuccess || defaultMessage;
            default:
                return defaultMessage;
        }
    }

    private setLoadingState(button: HTMLButtonElement, isLoading: boolean, action: string) {
        if (isLoading) {
            // Save original text
            const originalText = button.textContent || 'Submit';
            button.setAttribute('data-original-text', originalText);

            // Set loading text
            const loadingText = this.getCustomLoadingMessage(action) || 'Please wait...';
            button.textContent = loadingText;
        } else {
            button.textContent = button.getAttribute('data-original-text') || 'Submit';
        }
    }

    private getCustomLoadingMessage(action: string): string | undefined {
        switch (action) {
            case 'signin':
                return this.config.messages?.signinLoading;
            case 'signup':
                return this.config.messages?.signupLoading;
            case 'reset':
                return this.config.messages?.resetLoading;
            case 'update':
                return this.config.messages?.updateLoading;
            case 'resend':
                return this.config.messages?.resendLoading;
            default:
                return undefined;
        }
    }

    private injectStyles(): void {
        if (this.config.disableStyles) {
            return;
        }

        const existingStyle = document.getElementById('authiqa-styles');
        if (existingStyle) {
            existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = 'authiqa-styles';

        // Get the effective theme
        const effectiveTheme: 'light' | 'dark' =
            (!this.config.theme || this.config.theme === 'none') ? 'light' : this.config.theme;

        let styleContent = '';

        // Add base theme styles
        styleContent += getStyleContent(effectiveTheme);

        // Add component styles
        const componentStyles = getComponentStyles(effectiveTheme);
        styleContent += `
            /* Modal Styles */
            .authiqa-modal-overlay {
                ${Object.entries(componentStyles.modal.overlay)
                .map(([key, value]) => `${key}: ${value};`)
                .join('\n')}
            }
            .authiqa-modal-container {
                ${Object.entries(componentStyles.modal.container)
                .map(([key, value]) => `${key}: ${value};`)
                .join('\n')}
            }
            .authiqa-iframe {
                ${Object.entries(componentStyles.iframe)
                .map(([key, value]) => `${key}: ${value};`)
                .join('\n')}
            }
            /* Message Styles */
            .authiqa-message {
                ${Object.entries(componentStyles.message)
                .map(([key, value]) => `${key}: ${value};`)
                .join('\n')}
            }
            .authiqa-message-success {
                ${Object.entries(componentStyles.messageSuccess)
                .map(([key, value]) => `${key}: ${value};`)
                .join('\n')}
            }
            .authiqa-message-error {
                ${Object.entries(componentStyles.messageError)
                .map(([key, value]) => `${key}: ${value};`)
                .join('\n')}
            }
            .authiqa-message-show {
                ${Object.entries(componentStyles.messageShow)
                .map(([key, value]) => `${key}: ${value};`)
                .join('\n')}
            }
        `;

        // Add custom styles if customization is provided
        if (this.config.customization) {
            const styleGenerator = new StyleGenerator(this.config.customization);
            styleContent += styleGenerator.generateStyles();

            // Add page layout styles

            // Add terms container styles
            styleContent += generateTermsContainerStyles(this.config);
        }

        style.textContent = styleContent;
        document.head.appendChild(style);
    }

    private generateCustomStyles(customization: any): string {
        const { colors, typography, layout, buttons } = customization;

        return `
            .authiqa-container {
                background-color: ${colors.background};
                padding: ${layout.padding};
                margin: ${layout.margin};
                border-radius: ${layout.borderRadius};
                max-width: ${layout.maxWidth};
                font-family: ${typography.fontFamily};
            }

            .authiqa-container h1 {
                color: ${typography.titleColor};
                font-size: ${typography.titleSize};
            }

            .authiqa-container input {
                background-color: ${colors.inputBackground};
                color: ${colors.inputText};
                border: 1px solid ${colors.borderColor};
            }

            .authiqa-container button {
                background-color: ${colors.buttonBackground};
                color: ${colors.buttonText};
                height: ${buttons.height || '40px'};
                width: ${buttons.width || '100%'};
                border-radius: ${buttons.borderRadius};
            }
        `;
    }

    private updateTheme(newTheme: 'light' | 'dark'): void {
        if (this.config.disableStyles) return;

        const styleElement = document.getElementById('authiqa-styles');
        if (!styleElement) {
            this.injectStyles(); // Re-inject if missing
            return;
        }

        // Update body theme
        if (newTheme === 'dark') {
            document.body.setAttribute('data-theme', 'dark');
        } else {
            document.body.removeAttribute('data-theme');
        }

        // Update container theme if it exists
        const container = document.getElementById(this.config.container);
        if (container) {
            container.setAttribute('data-theme', newTheme);
        }
    }

    /**
     * Check if Google One Tap has been successfully authenticated in this session
     */
    private hasGoogleOneTapSuccessfulAuth(): boolean {
        try {
            return sessionStorage.getItem('authiqa_google_onetap_success') === 'true';
        } catch (error) {
            // Fallback to window variable if sessionStorage is not available
            return window._authiqaGoogleOneTapSuccessfulAuth === true;
        }
    }

    /**
     * Mark Google One Tap as successfully authenticated for this session
     */
    private markGoogleOneTapSuccessful(): void {
        try {
            sessionStorage.setItem('authiqa_google_onetap_success', 'true');
        } catch (error) {
            // Fallback to window variable if sessionStorage is not available
            window._authiqaGoogleOneTapSuccessfulAuth = true;
        }
    }

    /**
     * Properly dismiss Google One Tap to prevent persistence in SPAs
     */
    private dismissGoogleOneTap(): void {
        try {
            if ((window as any).google && (window as any).google.accounts && (window as any).google.accounts.id) {
                (window as any).google.accounts.id.cancel();
            }
            // Mark as dismissed in our state management
            window._authiqaGoogleOneTapDismissed = true;
            this.markGoogleOneTapSuccessful();
        } catch (error) {
            // Silently handle dismissal failures
        }
    }

    /**
     * Reset Google One Tap state for new sessions (used when user explicitly signs out)
     */
    private resetGoogleOneTapState(): void {
        window._authiqaGoogleOneTapDismissed = false;
        window._authiqaGoogleOneTapSuccessfulAuth = false;
        try {
            sessionStorage.removeItem('authiqa_google_onetap_success');
        } catch (error) {
            // Silently handle sessionStorage errors
        }
    }

    /**
     * Public method to reset Google One Tap state (useful for sign out scenarios)
     */
    public resetGoogleOneTap(): void {
        this.resetGoogleOneTapState();
    }

    public cleanup(): void {
        // Dismiss Google One Tap to prevent persistence in SPAs
        this.dismissGoogleOneTap();

        // Remove injected styles
        const styleElement = document.getElementById('authiqa-styles');
        if (styleElement) {
            styleElement.remove();
        }

        // Reset body styles
        document.body.style.backgroundColor = '';
        document.body.style.display = '';
        document.body.style.minHeight = '';
        document.body.style.alignItems = '';
        document.body.style.justifyContent = '';

        // Remove theme attributes
        document.body.removeAttribute('data-theme');
        const container = document.getElementById(this.config.container);
        if (container) {
            container.removeAttribute('data-theme');
            // Reset container styles
            container.style.marginTop = '';
            container.style.marginBottom = '';
            container.style.marginLeft = '';
            container.style.marginRight = '';
        }
    }

    private handleApiError(error: any): void {
        if (error?.error?.message) {
            // If it's an API error response, show the original message
            this.showMessage(error.error.message, 'error');
        } else if (error instanceof Error) {
            // Only for network errors
            this.showMessage('Unable to connect to the server', 'error');
        } else {
            // Fallback error
            this.showMessage('An unexpected error occurred', 'error');
        }
    }

    private validateDomain(organizationUrl: string): boolean {
        // Check for development mode
        if (this.isDevelopmentMode()) {
            return true;
        }

        // Extract domain from organization URL
        let orgDomain;
        try {
            orgDomain = new URL(organizationUrl).hostname;
        } catch (e) {
            console.error('Invalid organization URL:', organizationUrl);
            return false;
        }

        // Get current domain
        const currentDomain = window.location.hostname;

        // Check if current domain matches organization domain, is a subdomain,
        // or is an Authiqa domain
        return currentDomain === orgDomain ||
            currentDomain.endsWith('.' + orgDomain) ||
            currentDomain === 'authiqa.com' ||
            currentDomain === 'www.authiqa.com';
    }

    private isDevelopmentMode(): boolean {
        const scriptElement = document.querySelector('script[data-public-key]');
        if (!scriptElement) return false;

        const devMode = scriptElement.getAttribute('authiqa--dev-data-mode');
        return devMode === 'true';
    }

    private showUnauthorizedError(): void {
        const container = document.getElementById(this.config.container);
        if (!container) return;

        // Clear any existing content
        container.innerHTML = '';

        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'authiqa-error-container';

        const errorTitle = document.createElement('h2');
        errorTitle.textContent = 'Unauthorized Domain';
        errorTitle.style.color = '#e74c3c'; // Red color for error title

        const errorMessage = document.createElement('p');
        errorMessage.textContent = 'This widget can only be used on authorized domains. Please visit Authiqa and signin to update your organization related information';
        errorMessage.style.color = '#333333'; // Dark gray for message text

        const link = document.createElement('a');
        link.href = 'https://authiqa.com';
        link.textContent = 'Visit Authiqa';
        link.style.color = '#3498db'; // Blue for link

        errorDiv.appendChild(errorTitle);
        errorDiv.appendChild(errorMessage);
        errorDiv.appendChild(link);
        container.appendChild(errorDiv);

        // Add some basic styling
        const style = document.createElement('style');
        style.textContent = `
            .authiqa-error-container {
                padding: 20px;
                border: 1px solid #e74c3c;
                border-radius: 5px;
                background-color: #fef5f5;
                text-align: center;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            }
            .authiqa-error-container h2 {
                color: #e74c3c;
                margin-top: 0;
            }
            .authiqa-error-container p {
                color: #333333;
                margin-bottom: 15px;
            }
            .authiqa-error-container a {
                display: inline-block;
                margin-top: 15px;
                color: #3498db;
                text-decoration: none;
            }
            .authiqa-error-container a:hover {
                text-decoration: underline;
            }
        `;
        document.head.appendChild(style);
    }

    private shouldEnableGoogleOneTap(): boolean {
        // Only enable One Tap if:
        // 1. The config explicitly enables it (or undefined for backward compatibility)
        // 2. The current action is signin or signup
        return (this.config.enableGoogleOneTap !== false) &&
            (this.currentAction === 'signin' || this.currentAction === 'signup');
    }
}



// Register the widget on the window object
window.AuthiqaWidget = AuthiqaWidget;

// Then initialize
document.addEventListener('DOMContentLoaded', () => {
    try {
        const scriptElement = document.querySelector('script[data-public-key]'); // Changed from data-api-key

        if (!scriptElement) {
            console.error("Script tag with data-public-key not found."); // Changed error message
            return;
        }

        const publicKey = scriptElement.getAttribute('data-public-key'); // Changed from data-api-key

        // Get action from script attribute first
        let action = scriptElement.getAttribute('action');

        // If no action is provided in the script tag, check the entire URL
        if (!action) {
            const fullUrl = window.location.href;

            // List of valid actions to check for
            const validActions = ['signin', 'signup', 'verify', 'reset', 'update', 'resend'];

            // Find the first valid action that appears in the URL
            const foundAction = validActions.find(act => fullUrl.includes(act));

            if (foundAction) {
                action = foundAction;
            } else {
                // Default to signin if no valid action is found
                action = 'signin';
            }
        }

        const termsAndConditions = scriptElement.getAttribute('termsAndConditions');
        const privacy = scriptElement.getAttribute('privacy');
        const notificationSettings = scriptElement.getAttribute('notificationSettings');
        const theme = scriptElement.getAttribute('theme') || 'light';
        const disableStyles = scriptElement.getAttribute('disable-styles') === 'true';
        const verifyAuthPath = scriptElement.getAttribute('verifyAuthPath');
        const updatePasswordPath = scriptElement.getAttribute('updatePasswordPath');
        const resendAuthPath = scriptElement.getAttribute('resendAuthPath');
        const successAuthPath = scriptElement.getAttribute('successAuthPath');
        const signinAuthPath = scriptElement.getAttribute('signinAuthPath');
        const signupAuthPath = scriptElement.getAttribute('signupAuthPath');

        // Parse custom messages
        let messages;
        const messagesAttr = scriptElement.getAttribute('data-messages');
        if (messagesAttr) {
            try {
                messages = JSON.parse(messagesAttr);
            } catch (error) {
                console.error('Failed to parse custom messages:', error);
            }
        }

        // Parse customization
        let customization;
        const customizationAttr = scriptElement.getAttribute('data-customization');
        if (customizationAttr) {
            try {
                customization = JSON.parse(customizationAttr);
            } catch (error) {
                console.error('Failed to parse customization:', error);
            }
        }

        // Get enableGoogleOneTap from script attribute
        const enableGoogleOneTap = scriptElement.getAttribute('enable-google-one-tap') !== 'false';

        if (typeof window.AuthiqaWidget !== 'function') {
            console.error('AuthiqaWidget not properly registered');
            return;
        }

        const widgetConfig = {
            publicKey: publicKey || '', // Changed from apiKey
            container: 'authiqa',
            mode: 'popup',
            theme: theme as 'light' | 'dark' | 'none',
            disableStyles,
            organizationDomain: 'authiqa.com',
            enableGoogleOneTap,
            termsAndConditions,
            privacy,
            notificationSettings,
            messages,
            customization, // Add this line
            verifyAuthPath,
            updatePasswordPath,
            resendAuthPath,
            successAuthPath,
            signinAuthPath,
            signupAuthPath,
        };

        const widget = new window.AuthiqaWidget(widgetConfig);

        // Show form immediately, don't wait for initialize
        widget.show(action);

    } catch (error) {
        console.error('Error during widget initialization:', error);
    }
});

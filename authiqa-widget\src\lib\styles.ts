import { THEMES } from './constants';
import { WidgetConfig } from './types';

// Base theme styles
export const getStyleContent = (theme: 'light' | 'dark'): string => {
    return `
        /* Dynamically generated styles for ${theme} theme */
        :root {
            --authiqa-bg-color: ${theme === 'dark' ? '#18181b' : '#ffffff'};
            --authiqa-text-color: ${theme === 'dark' ? '#ffffff' : '#1a1a1a'};
            --authiqa-border-color: ${theme === 'dark' ? '#3f3f46' : '#e5e5e5'};
            --authiqa-input-bg: ${theme === 'dark' ? '#27272a' : '#ffffff'};
            --authiqa-button-bg: ${theme === 'dark' ? '#ffffff' : '#18181b'};
            --authiqa-button-text: ${theme === 'dark' ? '#18181b' : '#ffffff'};
        }
    `;
};

// Modal and message styles
export const getComponentStyles = (theme: 'light' | 'dark' = 'light') => {
    const themeColors = THEMES[theme];

    return {
        modal: {
            overlay: {
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                backgroundColor: themeColors.modalOverlay,
                zIndex: 1000
            },
            container: {
                position: 'relative',
                width: '500px',
                margin: '50px auto',
                backgroundColor: themeColors.background,
                color: themeColors.text,
                borderRadius: '8px',
                padding: '20px',
                border: `1px solid ${themeColors.border}`
            }
        },
        iframe: {
            border: 'none',
            width: '100%',
            height: '600px',
            backgroundColor: themeColors.background
        },
        message: {
            position: 'fixed',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            padding: '12px 24px',
            borderRadius: '4px',
            fontSize: '14px',
            fontWeight: '500',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        },
        messageSuccess: {
            backgroundColor: '#4CAF50',
            color: 'white'
        },
        messageError: {
            backgroundColor: '#f44336',
            color: 'white'
        },
        messageShow: {
            opacity: '1'
        }
    };
};



// Add this function to handle terms container styling
export const generateTermsContainerStyles = (config: WidgetConfig): string => {
    if (!config.customization?.colors) return '';
    
    const colors = config.customization.colors;
    
    return `
        /* Terms container styling */
        .authiqa-container .terms-container {
            display: flex;
            align-items: flex-start;
            margin: 0.75rem 0;
        }
        
        .authiqa-container .terms-container input[type="checkbox"] {
            margin-top: 3px;
            margin-right: 8px;
        }
        
        .authiqa-container .terms-container label {
            color: ${colors.inputText || '#333333'};
            font-size: 0.875rem;
            line-height: 1.4;
            margin: 0;
            flex: 1;
        }
        
        .authiqa-container .terms-container a {
            color: ${colors.buttonBackground || '#000000'};
            text-decoration: none;
        }
        
        .authiqa-container .terms-container a:hover {
            text-decoration: underline;
        }
    `;
}

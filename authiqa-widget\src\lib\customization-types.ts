export interface WidgetCustomization {
  // Layout
  layout: {
    padding?: string;
    paddingTop?: string; // Added to allow different top padding
    margin?: string;
    borderRadius?: string;
    maxWidth?: string;
    minWidth?: string; // NEW: Minimum width of the form
    width?: string; // NEW: Fixed width of the form
    height?: string; // NEW: Fixed height of the form
    minHeight?: string; // NEW: Minimum height of the form
    maxHeight?: string; // NEW: Maximum height of the form
  };
  
  // Colors
  colors: {
    background?: string;
    buttonBackground?: string;
    buttonText?: string;
    inputBackground?: string;
    inputText?: string;
    inputPlaceholder?: string;
    borderColor?: string;
  };
  
  // Typography
  typography: {
    titleText: {
      signinText?: string;
      signupText?: string;
      resetText?: string;
      updateText?: string;
      verifyText?: string;
      resendText?: string;
    };
    subtitleText?: {
      signinText?: string;
      signupText?: string;
      resetText?: string;
      updateText?: string;
      verifyText?: string;
      resendText?: string;
    };
    titleSize?: string;
    titleColor?: string;
    labelSize?: string;     // Add labelSize property
    fontFamily?: string;
    labelColor?: string; // NEW: Label text color
    labelFontWeight?: string; // NEW: Label font weight
    titleAlignment?: string; // NEW: Title alignment
    titleWeight?: string; // NEW: Title font weight
    titleLineHeight?: string; // NEW: Title line height
    termsText: {
      agreePrefix: string;    // "I agree with the"
      andConnector: string;   // "and"
      defaultPrefix: string;  // "default"
      linkText: {
        terms: string;        // "Terms of Service"
        privacy: string;      // "Privacy Policy"
        notifications: string; // "Notification Settings"
      };
      textColor?: string;     // Color for the terms text
      linkColor?: string;     // Color for the terms links
    };
    navTextColor?: string; // Navigation text color for light theme
    navTextColorDark?: string; // Navigation text color for dark theme
  };
  
  // Input Fields
  inputs: {
    emailPlaceholder?: string;
    passwordPlaceholder?: string;
    usernamePlaceholder?: string;
    confirmPasswordPlaceholder?: string;
    emailLabel?: string;
    passwordLabel?: string;
    usernameLabel?: string;
    confirmPasswordLabel?: string;
    // Customization for input fields
    borderRadius?: string;
    height?: string;
    width?: string;
    padding?: string;
    margin?: string;
    fontSize?: string;
    fontWeight?: string;
    focusBorderColor?: string;
    focusBoxShadow?: string;
    placeholderAlign?: string; // NEW: Placeholder text alignment
  };
  
  // Buttons
  buttons: {
    signinText?: string;
    signupText?: string;
    resetText?: string;
    updateText?: string;
    verifyText?: string;
    resendText?: string;
    height?: string;
    width?: string;      // NEW: Control button width
    padding?: string;    // NEW: Control button padding
    margin?: string;     // NEW: Control button margin
    borderRadius?: string;
    hoverBackground?: string; // NEW: Button hover background
  };

  // Navigation and forgot password links customization
  navLinks?: {
    // Texts
    signinPrompt?: string;
    signinLinkText?: string;
    signupPrompt?: string;
    signupLinkText?: string;
    forgotPrompt?: string;
    forgotLinkText?: string;
    // Styles
    fontSize?: string;
    color?: string;
    fontFamily?: string;
    textAlign?: string;
    marginTop?: string;
    marginBottom?: string;
    fontWeight?: string;
    linkColor?: string;
    linkFontWeight?: string;
    backToSigninPrompt?: string; // NEW: Prompt for 'Back to Sign In?'
  };
  pageLayout?: {
   
    formPosition?: 'center' | 'left' | 'right' | 'top' | 'bottom';
    formMarginTop?: string;
    formMarginBottom?: string;
    formMarginLeft?: string;
    formMarginRight?: string;
  };

}












